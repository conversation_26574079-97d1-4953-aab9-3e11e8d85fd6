import { Box, AppBar, Toolbar, Avatar, Typography, Chip, Container } from '@mui/material';

const AppBarLayout = ({
  title,
  icon,
  count,
  countLabel,
  children
}) => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Material Design AppBar - uses theme colors */}
      <AppBar
        position="static"
        color="primary"
        sx={{
          m: 0,
          p: 0
        }}
      >
        <Toolbar>
          <Avatar sx={{
            bgcolor: 'rgba(255,255,255,0.15)',
            color: 'white',
            mr: 2,
            border: '1px solid rgba(255,255,255,0.2)'
          }}>
            {icon}
          </Avatar>
          <Typography variant="h6" component="div" sx={{
            flexGrow: 1,
            fontWeight: 600,
            color: 'white'
          }}>
            {title}
          </Typography>
          {count !== undefined && (
            <Chip
              label={`${count} ${countLabel}`}
              sx={{
                bgcolor: 'rgba(255,255,255,0.15)',
                color: 'white',
                fontWeight: 500,
                border: '1px solid rgba(255,255,255,0.2)'
              }}
            />
          )}
        </Toolbar>
      </AppBar>

      {/* Content */}
      <Container maxWidth={false} sx={{ pt: 2, pb: 10, px: 2, m: 0, flex: 1 }}>
        {children}
      </Container>
    </Box>
  );
};

export default AppBarLayout;
