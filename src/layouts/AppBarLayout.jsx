import { Box, AppBar, Toolbar, Avatar, Typography, Chip, Container } from '@mui/material';

const AppBarLayout = ({
  title,
  icon,
  gradient,
  iconColor,
  count,
  countLabel,
  children
}) => {
  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      {/* Modern AppBar */}
      <AppBar
        position="static"
        sx={{
          background: gradient || 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)',
          boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
          m: 0,
          p: 0
        }}
      >
        <Toolbar>
          <Avatar sx={{
            bgcolor: 'rgba(255,255,255,0.15)',
            color: 'white',
            mr: 2,
            border: '2px solid rgba(255,255,255,0.2)'
          }}>
            {icon}
          </Avatar>
          <Typography variant="h6" component="div" sx={{
            flexGrow: 1,
            fontWeight: 600,
            color: 'white'
          }}>
            {title}
          </Typography>
          {count !== undefined && (
            <Chip
              label={`${count} ${countLabel}`}
              sx={{
                bgcolor: 'rgba(255,255,255,0.15)',
                color: 'white',
                fontWeight: 500,
                border: '1px solid rgba(255,255,255,0.2)'
              }}
            />
          )}
        </Toolbar>
      </AppBar>

      {/* Content */}
      <Container maxWidth={false} sx={{ pt: 2, pb: 10, px: 2, m: 0, flex: 1 }}>
        {children}
      </Container>
    </Box>
  );
};

export default AppBarLayout;
