/* Global reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  /* Only fix background for Telegram mini app */
  background-color: #ffffff !important;
}

#root {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100vh;
  overflow-x: hidden;
  /* Only fix background */
  background-color: #ffffff !important;
}

/* Override Telegram's CSS variables - only background related */
:root {
  --tg-theme-bg-color: #ffffff !important;
  --tg-theme-secondary-bg-color: #f5f5f5 !important;
}

/* Only fix main container backgrounds, preserve all other colors */
.MuiContainer-root {
  background-color: transparent !important;
}

/* Preserve all gradients and colors in AppBars */
.MuiAppBar-root {
  /* Don't override - let gradients work */
}

/* Preserve all card colors and effects */
.MuiCard-root {
  /* Don't override - let colorful cards work */
}

/* Preserve all chip colors */
.MuiChip-root {
  /* Don't override - let colorful chips work */
}

/* Only fix bottom navigation background */
.MuiBottomNavigation-root {
  background-color: #ffffff !important;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
