import { createTheme } from '@mui/material/styles';

// Modern minimal theme with proper light/dark mode support
export const createModernTheme = (mode = 'light') => {
  const isLight = mode === 'light';

  return createTheme({
    palette: {
      mode,
      primary: {
        main: '#2563eb', // Modern blue
        light: '#3b82f6',
        dark: '#1d4ed8',
        contrastText: '#ffffff',
      },
      secondary: {
        main: '#64748b', // Modern slate
        light: '#94a3b8',
        dark: '#475569',
        contrastText: '#ffffff',
      },
      background: {
        default: isLight ? '#ffffff' : '#0f172a',
        paper: isLight ? '#ffffff' : '#1e293b',
      },
      text: {
        primary: isLight ? '#0f172a' : '#f8fafc',
        secondary: isLight ? '#64748b' : '#94a3b8',
      },
      divider: isLight ? '#e2e8f0' : '#334155',
      action: {
        hover: isLight ? '#f1f5f9' : '#334155',
        selected: isLight ? '#e2e8f0' : '#475569',
      },
    },
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontWeight: 700,
        fontSize: '2.5rem',
      },
      h2: {
        fontWeight: 600,
        fontSize: '2rem',
      },
      h3: {
        fontWeight: 600,
        fontSize: '1.75rem',
      },
      h4: {
        fontWeight: 600,
        fontSize: '1.5rem',
      },
      h5: {
        fontWeight: 600,
        fontSize: '1.25rem',
      },
      h6: {
        fontWeight: 600,
        fontSize: '1.125rem',
      },
      body1: {
        fontSize: '1rem',
        lineHeight: 1.6,
      },
      body2: {
        fontSize: '0.875rem',
        lineHeight: 1.5,
      },
    },
    shape: {
      borderRadius: 8, // Material Design standard
    },
    components: {
      MuiCssBaseline: {
        styleOverrides: {
          body: {
            backgroundColor: isLight ? '#ffffff' : '#0f172a',
            color: isLight ? '#0f172a' : '#f8fafc',
            transition: 'background-color 0.3s ease, color 0.3s ease',
          },
          html: {
            backgroundColor: isLight ? '#ffffff' : '#0f172a',
          },
          '#root': {
            backgroundColor: isLight ? '#ffffff' : '#0f172a',
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 8, // Material Design standard
            boxShadow: isLight
              ? '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)'
              : '0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3)',
            border: `1px solid ${isLight ? '#e2e8f0' : '#334155'}`,
            transition: 'background-color 0.3s ease, border-color 0.3s ease', // Remove hover animations
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            textTransform: 'none',
            fontWeight: 500,
            boxShadow: 'none',
            '&:hover': {
              boxShadow: 'none',
            },
          },
          contained: {
            background: 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%)',
            },
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            background: isLight
              ? 'linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%)'
              : 'linear-gradient(135deg, #1e293b 0%, #0f172a 100%)',
            boxShadow: isLight
              ? '0 1px 3px 0 rgb(0 0 0 / 0.1)'
              : '0 4px 6px -1px rgb(0 0 0 / 0.3)',
          },
        },
      },
      MuiBottomNavigation: {
        styleOverrides: {
          root: {
            backgroundColor: isLight ? '#ffffff' : '#1e293b',
            borderTop: `1px solid ${isLight ? '#e2e8f0' : '#334155'}`,
            boxShadow: isLight
              ? '0 -1px 3px 0 rgb(0 0 0 / 0.1)'
              : '0 -4px 6px -1px rgb(0 0 0 / 0.3)',
          },
        },
      },
      MuiBottomNavigationAction: {
        styleOverrides: {
          root: {
            color: isLight ? '#64748b' : '#94a3b8',
            '&.Mui-selected': {
              color: '#2563eb',
            },
            '& .MuiBottomNavigationAction-label': {
              fontSize: '0.75rem',
              fontWeight: 500,
              opacity: 1, // Always show labels
            },
            '&:not(.Mui-selected) .MuiBottomNavigationAction-label': {
              opacity: 1, // Always show labels even when not selected
            },
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            fontWeight: 500,
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundColor: isLight ? '#ffffff' : '#1e293b',
            backgroundImage: 'none',
          },
        },
      },
    },
  });
};

// Default light theme
const modernTheme = createModernTheme('light');

export default modernTheme;
